<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GigGenius University</title>
    <style>
        /* Base Tailwind-like styles */
        :root {
            --background: 0 0% 100%; /* White */
            --foreground: 202 84% 27%; /* Dark Blue #0d547e */
            --card: 0 0% 100%;
            --card-foreground: 202 84% 27%;
            --popover: 0 0% 100%;
            --popover-foreground: 202 84% 27%;
            --primary: 326 63% 47%; /* Pink/Magenta #bd328d */
            --primary-foreground: 0 0% 100%; /* White text on primary */
            --secondary: 202 84% 27%; /* Dark Blue #0d547e */
            --secondary-foreground: 0 0% 100%; /* White text on secondary */
            --muted: 0 0% 96.1%; /* Light gray */
            --muted-foreground: 0 0% 40%; /* Mid gray */
            --accent: 326 63% 47%; /* Pink/Magenta #bd328d for accents */
            --accent-foreground: 0 0% 100%;
            --destructive: 0 84.2% 60.2%;
            --destructive-foreground: 0 0% 100%;
            --border: 0 0% 89.8%; /* Light gray border */
            --input: 0 0% 89.8%; 
            --ring: 326 63% 47%; /* Ring matches primary */
            --radius: 0.5rem; /* Standard radius */
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
            min-height: 100vh;
            line-height: 1.5;
        }

        .container {
            width: 100%;
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 1rem;
        }

        .main-container {
            padding: 2rem 1rem;
            max-width: 1200px;
            margin: 0 auto;
        }

        /* Typography */
        h1, h2, h3, h4, h5, h6 {
            font-weight: 700;
            color: hsl(var(--secondary));
        }

        h1 {
            font-size: 2.5rem;
            margin-bottom: 1rem;
        }

        h2 {
            font-size: 2rem;
            margin-bottom: 0.75rem;
        }

        h3 {
            font-size: 1.5rem;
            margin-bottom: 0.5rem;
        }

        p {
            margin-bottom: 1rem;
        }

        /* Navbar */
        .navbar {
            background-color: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
            padding: 1rem;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            position: sticky;
            top: 0;
            z-index: 50;
        }

        .navbar-container {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .navbar-logo {
            font-size: 1.5rem;
            font-weight: bold;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
        }

        .navbar-logo svg {
            margin-right: 0.75rem;
        }

        .navbar-links {
            display: flex;
            gap: 0.5rem;
        }

        /* Buttons */
        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            border-radius: var(--radius);
            font-weight: 500;
            padding: 0.5rem 1rem;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }

        .btn-primary {
            background-color: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
        }

        .btn-primary:hover {
            opacity: 0.9;
        }

        .btn-secondary {
            background-color: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
        }

        .btn-secondary:hover {
            opacity: 0.9;
        }

        .btn-outline {
            background-color: transparent;
            border: 1px solid hsl(var(--border));
            color: hsl(var(--secondary));
        }

        .btn-outline:hover {
            background-color: hsl(var(--accent) / 0.1);
            color: hsl(var(--primary));
            border-color: hsl(var(--primary));
        }

        .btn-ghost {
            background-color: transparent;
            color: hsl(var(--primary-foreground));
        }

        .btn-ghost:hover {
            background-color: hsla(0, 0%, 100%, 0.2);
        }

        .btn-lg {
            padding: 0.75rem 1.5rem;
            font-size: 1.125rem;
        }

        /* Cards */
        .card {
            background: hsl(var(--card) / 0.85);
            border-radius: var(--radius);
            border: 1px solid hsl(var(--border) / 0.3);
            box-shadow: 0 4px 18px 0 hsl(var(--primary) / 0.1);
            overflow: hidden;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 20px 0 hsl(var(--primary) / 0.15);
        }

        .card-header {
            padding: 1rem;
        }

        .card-content {
            padding: 1rem;
            padding-top: 0;
        }

        .card-footer {
            padding: 1rem;
            border-top: 1px solid hsl(var(--border));
            display: flex;
            justify-content: space-between;
        }

        /* Grid */
        .grid {
            display: grid;
            gap: 1.5rem;
        }

        .grid-cols-1 {
            grid-template-columns: repeat(1, 1fr);
        }

        @media (min-width: 640px) {
            .sm\:grid-cols-2 {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (min-width: 768px) {
            .md\:grid-cols-2 {
                grid-template-columns: repeat(2, 1fr);
            }
            .md\:grid-cols-3 {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (min-width: 1024px) {
            .lg\:grid-cols-3 {
                grid-template-columns: repeat(3, 1fr);
            }
            .lg\:grid-cols-4 {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        /* Flex */
        .flex {
            display: flex;
        }

        .flex-col {
            flex-direction: column;
        }

        .items-center {
            align-items: center;
        }

        .justify-between {
            justify-content: space-between;
        }

        .justify-center {
            justify-content: center;
        }

        .space-x-2 > * + * {
            margin-left: 0.5rem;
        }

        .space-x-4 > * + * {
            margin-left: 1rem;
        }

        .space-y-4 > * + * {
            margin-top: 1rem;
        }

        /* Utilities */
        .text-center {
            text-align: center;
        }

        .text-brand-primary {
            color: hsl(var(--primary));
        }

        .text-brand-secondary {
            color: hsl(var(--secondary));
        }

        .text-muted {
            color: hsl(var(--muted-foreground));
        }

        .bg-brand-primary {
            background-color: hsl(var(--primary));
            color: hsl(var(--primary-foreground));
        }

        .bg-brand-secondary {
            background-color: hsl(var(--secondary));
            color: hsl(var(--secondary-foreground));
        }

        .rounded-full {
            border-radius: 9999px;
        }

        .rounded-lg {
            border-radius: var(--radius);
        }

        .shadow-lg {
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
        }

        .py-4 {
            padding-top: 1rem;
            padding-bottom: 1rem;
        }

        .py-8 {
            padding-top: 2rem;
            padding-bottom: 2rem;
        }

        .py-16 {
            padding-top: 4rem;
            padding-bottom: 4rem;
        }

        .px-4 {
            padding-left: 1rem;
            padding-right: 1rem;
        }

        .mb-2 {
            margin-bottom: 0.5rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .mb-6 {
            margin-bottom: 1.5rem;
        }

        .mb-8 {
            margin-bottom: 2rem;
        }

        .mb-12 {
            margin-bottom: 3rem;
        }

        .mt-4 {
            margin-top: 1rem;
        }

        .mt-8 {
            margin-top: 2rem;
        }

        .mr-2 {
            margin-right: 0.5rem;
        }

        .ml-2 {
            margin-left: 0.5rem;
        }

        .w-full {
            width: 100%;
        }

        .h-auto {
            height: auto;
        }

        .max-w-3xl {
            max-width: 48rem;
        }

        .max-w-4xl {
            max-width: 56rem;
        }

        .mx-auto {
            margin-left: auto;
            margin-right: auto;
        }

        .text-xl {
            font-size: 1.25rem;
        }

        .text-2xl {
            font-size: 1.5rem;
        }

        .text-3xl {
            font-size: 1.875rem;
        }

        .text-4xl {
            font-size: 2.25rem;
        }

        .text-5xl {
            font-size: 3rem;
        }

        .text-7xl {
            font-size: 4.5rem;
        }

        .font-bold {
            font-weight: 700;
        }

        .font-semibold {
            font-weight: 600;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .text-xs {
            font-size: 0.75rem;
        }

        .text-lg {
            font-size: 1.125rem;
        }

        .text-md {
            font-size: 1rem;
        }

        /* Avatar */
        .avatar {
            position: relative;
            width: 2.5rem;
            height: 2.5rem;
            border-radius: 9999px;
            overflow: hidden;
            flex-shrink: 0;
        }

        .avatar-lg {
            width: 6rem;
            height: 6rem;
        }

        .avatar img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .avatar-fallback {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background-color: hsl(var(--muted));
            color: hsl(var(--muted-foreground));
            font-weight: 500;
        }

        /* Modal */
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 100;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.2s ease;
        }

        .modal-backdrop.show {
            opacity: 1;
            pointer-events: auto;
        }

        .modal-content {
            background: hsl(var(--background));
            border-radius: var(--radius);
            box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
            width: 90%;
            max-width: 500px;
            max-height: 90vh;
            overflow-y: auto;
            padding: 1.5rem;
            transform: scale(0.95);
            transition: transform 0.2s ease;
        }

        .modal-backdrop.show .modal-content {
            transform: scale(1);
        }

        .modal-header {
            margin-bottom: 1rem;
        }

        .modal-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: hsl(var(--secondary));
        }

        .modal-body {
            margin-bottom: 1.5rem;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 0.5rem;
        }

        /* Form elements */
        .form-group {
            margin-bottom: 1rem;
        }

        .form-label {
            display: block;
            margin-bottom: 0.5rem;
            font-weight: 500;
            color: hsl(var(--secondary));
        }

        .form-control {
            width: 100%;
            padding: 0.5rem 0.75rem;
            border-radius: var(--radius);
            border: 1px solid hsl(var(--border));
            background-color: hsl(var(--background));
            color: hsl(var(--foreground));
            transition: border-color 0.2s ease, box-shadow 0.2s ease;
        }

        .form-control:focus {
            outline: none;
            border-color: hsl(var(--ring));
            box-shadow: 0 0 0 2px hsl(var(--ring) / 0.3);
        }

        /* Tabs */
        .tabs {
            display: flex;
            border-bottom: 1px solid hsl(var(--border));
            margin-bottom: 1rem;
        }

        .tab {
            padding: 0.75rem 1rem;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.2s ease;
        }

        .tab.active {
            border-bottom-color: hsl(var(--primary));
            color: hsl(var(--primary));
        }

        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* Toast notifications */
        .toast-container {
            position: fixed;
            bottom: 1rem;
            right: 1rem;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .toast {
            background-color: hsl(var(--background));
            border: 1px solid hsl(var(--border));
            border-radius: var(--radius);
            padding: 1rem;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: flex-start;
            max-width: 350px;
            animation: slideIn 0.3s ease forwards;
        }

        .toast-success {
            border-left: 4px solid #10b981;
        }

        .toast-error {
            border-left: 4px solid #ef4444;
        }

        .toast-content {
            flex: 1;
        }

        .toast-title {
            font-weight: 600;
            margin-bottom: 0.25rem;
        }

        .toast-close {
            background: none;
            border: none;
            cursor: pointer;
            color: hsl(var(--muted-foreground));
        }

        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        /* Footer */
        .footer {
            background-color: hsl(var(--muted));
            color: hsl(var(--muted-foreground));
            padding: 2rem 0;
            margin-top: 3rem;
            text-align: center;
        }

        /* Hero section */
        .hero {
            padding: 4rem 0;
            background: linear-gradient(to bottom right, hsl(var(--primary) / 0.05), hsl(var(--accent) / 0.05), hsl(var(--secondary) / 0.05));
            border-radius: var(--radius);
            box-shadow: 0 10px 30px -5px rgba(0, 0, 0, 0.1);
            margin-bottom: 3rem;
            text-align: center;
        }

        /* Course cards */
        .course-grid {
            display: grid;
            gap: 1.5rem;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
        }

        .course-card {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        .course-image {
            aspect-ratio: 16 / 9;
            width: 100%;
            object-fit: cover;
            border-radius: var(--radius) var(--radius) 0 0;
        }

        .course-content {
            flex: 1;
            display: flex;
            flex-direction: column;
        }

        .course-footer {
            margin-top: auto;
        }

        .badge {
            display: inline-block;
            padding: 0.25rem 0.5rem;
            border-radius: 9999px;
            font-size: 0.75rem;
            font-weight: 500;
            background-color: hsl(var(--muted));
            color: hsl(var(--muted-foreground));
        }

        .badge-primary {
            background-color: hsl(var(--primary) / 0.1);
            color: hsl(var(--primary));
        }

        .badge-secondary {
            background-color: hsl(var(--secondary) / 0.1);
            color: hsl(var(--secondary));
        }

        .badge-success {
            background-color: rgba(16, 185, 129, 0.1);
            color: #10b981;
        }

        /* Icons */
        .icon {
            display: inline-block;
            width: 1.5rem;
            height: 1.5rem;
            stroke-width: 0;
            stroke: currentColor;
            fill: currentColor;
            vertical-align: middle;
        }

        .icon-sm {
            width: 1rem;
            height: 1rem;
        }

        .icon-lg {
            width: 2rem;
            height: 2rem;
        }

        /* Hide elements */
        .hidden {
            display: none !important;
        }

        /* Responsive */
        @media (min-width: 768px) {
            .md\:flex-row {
                flex-direction: row;
            }
            
            .md\:text-5xl {
                font-size: 3rem;
            }
            
            .md\:text-2xl {
                font-size: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <!-- Navbar -->
    <nav class="navbar">
        <div class="container navbar-container">
            <a href="#" class="navbar-logo" onclick="navigateTo('home')">
                <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon">
                    <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                    <line x1="3" y1="9" x2="21" y2="9"></line>
                    <line x1="9" y1="21" x2="9" y2="9"></line>
                </svg>
                GigGenius University
            </a>
            <div class="navbar-links">
                <a href="#" class="btn btn-ghost" onclick="navigateTo('home')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                        <path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                        <polyline points="9 22 9 12 15 12 15 22"></polyline>
                    </svg>
                    Home
                </a>
                <div id="auth-buttons">
                    <button class="btn btn-ghost" onclick="openAuthModal('signup')">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <line x1="19" y1="8" x2="19" y2="14"></line>
                            <line x1="22" y1="11" x2="16" y2="11"></line>
                        </svg>
                        Sign Up
                    </button>
                    <button class="btn btn-secondary" onclick="openAuthModal('login')">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                            <polyline points="10 17 15 12 10 7"></polyline>
                            <line x1="15" y1="12" x2="3" y2="12"></line>
                        </svg>
                        Login
                    </button>
                </div>
                <div id="user-menu" class="hidden">
                    <button class="btn btn-ghost" id="dashboard-btn">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                            <path d="M21 12V7H5a2 2 0 0 1 0-4h14v4"></path>
                            <path d="M3 5v14a2 2 0 0 0 2 2h16v-5"></path>
                            <path d="M18 12a2 2 0 0 0 0 4h4v-4Z"></path>
                        </svg>
                        Dashboard
                    </button>
                    <button class="btn btn-secondary" onclick="logout()">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                            <polyline points="16 17 21 12 16 7"></polyline>
                            <line x1="21" y1="12" x2="9" y2="12"></line>
                        </svg>
                        Logout
                    </button>
                </div>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <main id="app-container">
        <!-- Content will be dynamically inserted here -->
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <p class="text-brand-secondary">&copy; <span id="current-year"></span> GigGenius University. All rights reserved.</p>
            <p class="text-sm mt-4 text-brand-secondary">Empowering Minds, Shaping Futures.</p>
        </div>
    </footer>

    <!-- Auth Modal -->
    <div class="modal-backdrop" id="auth-modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title" id="auth-modal-title">Login to GigGenius</h2>
                <p class="text-muted" id="auth-modal-description">Select your role to access your dashboard.</p>
            </div>
            <div class="modal-body">
                <div class="tabs" id="auth-tabs">
                    <div class="tab active" data-tab="login" onclick="switchAuthTab('login')">Login</div>
                    <div class="tab" data-tab="signup" onclick="switchAuthTab('signup')">Sign Up</div>
                </div>
                
                <div class="tab-content active" id="login-tab">
                    <p class="text-sm text-muted mb-4">For demonstration purposes, click a button below to log in as a pre-defined user.</p>
                    <button class="btn btn-primary w-full mb-2" onclick="login('genius')">Login as Demo Genius</button>
                    <button class="btn btn-secondary w-full" onclick="login('mentor')">Login as Demo Mentor</button>
                </div>
                
                <div class="tab-content" id="signup-tab">
                    <div class="form-group">
                        <label class="form-label" for="name-signup">Full Name</label>
                        <input type="text" id="name-signup" class="form-control" placeholder="Your Name">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="email-signup">Email</label>
                        <input type="email" id="email-signup" class="form-control" placeholder="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label class="form-label" for="password-signup">Password</label>
                        <input type="password" id="password-signup" class="form-control" placeholder="••••••••">
                    </div>
                    <p class="text-xs text-muted text-center mt-4 mb-4">Select your role below to complete mock signup.</p>
                    <button class="btn btn-primary w-full mb-2" onclick="signup('genius')">Sign Up as Genius</button>
                    <button class="btn btn-secondary w-full" onclick="signup('mentor')">Sign Up as Mentor</button>
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-outline" onclick="closeAuthModal()">Cancel</button>
            </div>
        </div>
    </div>

    <!-- Toast Container -->
    <div class="toast-container" id="toast-container"></div>

    <script>
        // Current state
        let currentPage = 'home';
        let currentUser = null;
        let userRole = null;
        let isAuthenticated = false;

        // Constants for localStorage keys
        const MENTORS_KEY = 'giggenius_mentors_data';
        const COURSES_KEY = 'giggenius_courses_data';
        const ONE_ON_ONE_KEY = 'giggenius_one_on_one_data';
        const GENIUSES_KEY = 'giggenius_geniuses_data';
        const ENROLLMENTS_KEY = 'giggenius_enrollments_data';
        const USER_KEY = 'giggenius_user';

        // Initialize the app
        document.addEventListener('DOMContentLoaded', function() {
            // Set current year in footer
            document.getElementById('current-year').textContent = new Date().getFullYear();
            
            // Initialize data
            initializeData();
            
            // Check if user is logged in
            checkAuthStatus();
            
            // Load the initial page
            navigateTo('home');
        });

        // Navigation
        function navigateTo(page) {
            // If trying to access protected pages
            if ((page === 'genius-dashboard' || page === 'mentor-dashboard') && !isAuthenticated) {
                showToast('Login Required', 'Please log in to access this page.', 'error');
                openAuthModal('login');
                return;
            }

            // If trying to access role-specific pages
            if (page === 'genius-dashboard' && userRole !== 'genius') {
                showToast('Access Denied', 'This page is for Geniuses only.', 'error');
                return;
            }

            if (page === 'mentor-dashboard' && userRole !== 'mentor') {
                showToast('Access Denied', 'This page is for Mentors only.', 'error');
                return;
            }

            currentPage = page;
            
            // Render the appropriate page
            const appContainer = document.getElementById('app-container');
            
            switch(page) {
                case 'home':
                    appContainer.innerHTML = renderHomePage();
                    break;
                case 'genius-dashboard':
                    appContainer.innerHTML = renderGeniusDashboard();
                    break;
                case 'mentor-dashboard':
                    appContainer.innerHTML = renderMentorDashboard();
                    break;
                default:
                    appContainer.innerHTML = renderNotFoundPage();
            }
        }

        // Authentication
        function checkAuthStatus() {
            const storedUser = localStorage.getItem(USER_KEY);
            if (storedUser) {
                currentUser = JSON.parse(storedUser);
                userRole = currentUser.role;
                isAuthenticated = true;
                
                // Update UI
                document.getElementById('auth-buttons').classList.add('hidden');
                document.getElementById('user-menu').classList.remove('hidden');
                
                // Set dashboard button action
                const dashboardBtn = document.getElementById('dashboard-btn');
                dashboardBtn.innerHTML = `
                    ${userRole === 'genius' ? 
                        '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><path d="M13 2v7h7"></path></svg>' : 
                        '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><circle cx="12" cy="8" r="5"></circle><path d="M20 21a8 8 0 1 0-16 0"></path></svg>'
                    }
                    Dashboard
                `;
                dashboardBtn.onclick = function() {
                    navigateTo(userRole === 'genius' ? 'genius-dashboard' : 'mentor-dashboard');
                };
            }
        }

        function openAuthModal(tab) {
            const modal = document.getElementById('auth-modal');
            modal.classList.add('show');
            switchAuthTab(tab);
        }

        function closeAuthModal() {
            const modal = document.getElementById('auth-modal');
            modal.classList.remove('show');
        }

        function switchAuthTab(tab) {
            // Update tabs
            document.querySelectorAll('#auth-tabs .tab').forEach(t => {
                t.classList.remove('active');
            });
            document.querySelector(`#auth-tabs .tab[data-tab="${tab}"]`).classList.add('active');
            
            // Update content
            document.querySelectorAll('.tab-content').forEach(c => {
                c.classList.remove('active');
            });
            document.getElementById(`${tab}-tab`).classList.add('active');
            
            // Update title and description
            const title = document.getElementById('auth-modal-title');
            const description = document.getElementById('auth-modal-description');
            
            if (tab === 'login') {
                title.textContent = 'Login to GigGenius';
                description.textContent = 'Select your role to access your dashboard.';
            } else {
                title.textContent = 'Create Account';
                description.textContent = 'Join GigGenius University today! (Mock Signup)';
            }
        }

        function login(role) {
            let userData;
            
            if (role === 'genius') {
                const geniuses = getGeniuses();
                userData = geniuses.length > 0 ? geniuses[0] : null;
            } else {
                const mentors = getMentors();
                userData = mentors.length > 0 ? mentors[0] : null;
            }
            
            if (userData) {
                const userToStore = { ...userData, role };
                localStorage.setItem(USER_KEY, JSON.stringify(userToStore));
                currentUser = userToStore;
                userRole = role;
                isAuthenticated = true;
                
                // Update UI
                document.getElementById('auth-buttons').classList.add('hidden');
                document.getElementById('user-menu').classList.remove('hidden');
                
                // Set dashboard button action
                const dashboardBtn = document.getElementById('dashboard-btn');
                dashboardBtn.innerHTML = `
                    ${role === 'genius' ? 
                        '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z"></path><path d="M13 2v7h7"></path></svg>' : 
                        '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2"><circle cx="12" cy="8" r="5"></circle><path d="M20 21a8 8 0 1 0-16 0"></path></svg>'
                    }
                    Dashboard
                `;
                dashboardBtn.onclick = function() {
                    navigateTo(role === 'genius' ? 'genius-dashboard' : 'mentor-dashboard');
                };
                
                closeAuthModal();
                showToast('Login Successful', `Welcome back, ${userData.name}!`, 'success');
                navigateTo(role === 'genius' ? 'genius-dashboard' : 'mentor-dashboard');
                
                return true;
            }
            
            showToast('Login Failed', 'Could not log in. Please try again.', 'error');
            return false;
        }

        function signup(role) {
            const name = document.getElementById('name-signup').value;
            const email = document.getElementById('email-signup').value;
            const password = document.getElementById('password-signup').value;
            
            if (!name || !email || !password) {
                showToast('Signup Failed', 'Please fill in all fields.', 'error');
                return;
            }
            
            showToast('Signup Successful (Mock)', 'You can now log in with the demo accounts.', 'success');
            closeAuthModal();
            
            // Reset form
            document.getElementById('name-signup').value = '';
            document.getElementById('email-signup').value = '';
            document.getElementById('password-signup').value = '';
        }

        function logout() {
            localStorage.removeItem(USER_KEY);
            currentUser = null;
            userRole = null;
            isAuthenticated = false;
            
            // Update UI
            document.getElementById('auth-buttons').classList.remove('hidden');
            document.getElementById('user-menu').classList.add('hidden');
            
            showToast('Logout Successful', 'You have been logged out.', 'success');
            navigateTo('home');
        }

        // Toast notifications
        function showToast(title, message, type = 'success') {
            const toastContainer = document.getElementById('toast-container');
            const toastId = 'toast-' + Date.now();
            
            const toast = document.createElement('div');
            toast.id = toastId;
            toast.className = `toast ${type === 'success' ? 'toast-success' : 'toast-error'}`;
            toast.innerHTML = `
                <div class="toast-content">
                    <div class="toast-title">${title}</div>
                    <div class="toast-message">${message}</div>
                </div>
                <button class="toast-close" onclick="closeToast('${toastId}')">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            `;
            
            toastContainer.appendChild(toast);
            
            // Auto-remove after 5 seconds
            setTimeout(() => {
                closeToast(toastId);
            }, 5000);
        }

        function closeToast(toastId) {
            const toast = document.getElementById(toastId);
            if (toast) {
                toast.style.opacity = '0';
                setTimeout(() => {
                    toast.remove();
                }, 300);
            }
        }

        // Data Service
        function initializeData() {
            if (!localStorage.getItem(MENTORS_KEY)) {
                localStorage.setItem(MENTORS_KEY, JSON.stringify(getInitialData().mentors));
            }
            if (!localStorage.getItem(GENIUSES_KEY)) {
                localStorage.setItem(GENIUSES_KEY, JSON.stringify(getInitialData().geniuses));
            }
            if (!localStorage.getItem(COURSES_KEY)) {
                localStorage.setItem(COURSES_KEY, JSON.stringify(getInitialData().courses));
            }
            if (!localStorage.getItem(ONE_ON_ONE_KEY)) {
                localStorage.setItem(ONE_ON_ONE_KEY, JSON.stringify(getInitialData().oneOnOneSessions));
            }
            if (!localStorage.getItem(ENROLLMENTS_KEY)) {
                localStorage.setItem(ENROLLMENTS_KEY, JSON.stringify(getInitialData().enrollments));
            }
        }

        function getInitialData() {
            return {
                mentors: [
                    { id: 'mentor1', name: 'Prof. Eva Rostova', specialty: 'Advanced Quantum Physics', avatarSeed: 'EvaRostova', isMentor: true },
                    { id: 'mentor2', name: 'Dr. Kenji Tanaka', specialty: 'Cybersecurity & Ethical Hacking', avatarSeed: 'KenjiTanaka', isMentor: true },
                    { id: 'mentor3', name: 'Maria Garcia', specialty: 'Digital Marketing Mastery', avatarSeed: 'MariaGarcia', isMentor: true },
                    { id: 'mentor4', name: 'Samuel Green', specialty: 'Sustainable Architecture', avatarSeed: 'SamuelGreen', isMentor: true },
                    { id: 'mentor5', name: 'Linda Holloway', specialty: 'Creative Writing Workshops', avatarSeed: 'LindaHolloway', isMentor: true },
                    { id: 'mentor6', name: 'Raj Patel', specialty: 'AI Fundamentals', avatarSeed: 'RajPatel', isMentor: true },
                    { id: 'mentor7', name: 'Chloe Dubois', specialty: 'UX/UI Design Principles', avatarSeed: 'ChloeDubois', isMentor: true },
                    { id: 'mentor8', name: 'Ben Carter', specialty: 'Mobile App Development', avatarSeed: 'BenCarter', isMentor: true },
                ],
                geniuses: [
                    { id: 'genius1', name: 'Alex Chen', learningFocus: 'AI & Machine Learning', avatarSeed: 'AlexChen', isMentor: false },
                    { id: 'genius2', name: 'Brenda Smith', learningFocus: 'Web Development', avatarSeed: 'BrendaSmith', isMentor: false },
                    { id: 'genius3', name: 'Carlos Diaz', learningFocus: 'Data Science', avatarSeed: 'CarlosDiaz', isMentor: false },
                ],
                courses: [
                    { id: 'c1', mentorId: 'mentor1', title: 'Quantum Entanglement Explained', description: 'Delve into the mysteries of quantum entanglement with Prof. Rostova.', price: 299, category: 'Physics', isFree: false, attachmentUrl: 'https://example.com/quantum-entanglement.pdf', previewImageUrl: 'https://images.unsplash.com/photo-1598087258861-5a3838153290?auto=format&fit=crop&w=500&q=60' },
                    { id: 'c2', mentorId: 'mentor2', title: 'Introduction to Cybersecurity', description: 'Learn the fundamentals of protecting digital assets with Dr. Tanaka.', price: 0, category: 'Cybersecurity', isFree: true, attachmentUrl: 'https://example.com/cybersecurity-intro.pdf', previewImageUrl: 'https://images.unsplash.com/photo-1526374965328-7f61d4dc18c5?auto=format&fit=crop&w=500&q=60' },
                    { id: 'c3', mentorId: 'mentor3', title: 'SEO Strategies for 2025', description: 'Boost your online presence with cutting-edge SEO techniques by Maria Garcia.', price: 199, category: 'Marketing', isFree: false, attachmentUrl: 'https://example.com/seo-strategies.zip', previewImageUrl: 'https://images.unsplash.com/photo-1559028012-7de4504a6340?auto=format&fit=crop&w=500&q=60' },
                    { id: 'c4', mentorId: 'mentor1', title: 'The Standard Model of Particle Physics', description: 'An overview of the fundamental particles and forces.', price: 0, category: 'Physics', isFree: true, attachmentUrl: 'https://example.com/standard-model.pdf', previewImageUrl: 'https://images.unsplash.com/photo-1614926194901-385c2cb405a2?auto=format&fit=crop&w=500&q=60' },
                    { id: 'c5', mentorId: 'mentor4', title: 'Eco-Friendly Building Materials', description: 'Discover sustainable materials for modern architecture with Samuel Green.', price: 149, category: 'Architecture', isFree: false, attachmentUrl: 'https://example.com/eco-materials.pdf', previewImageUrl: 'https://images.unsplash.com/photo-1487958449943-2429e8be8625?auto=format&fit=crop&w=500&q=60' },
                    { id: 'c6', mentorId: 'mentor6', title: 'Beginner AI Concepts', description: 'A gentle introduction to Artificial Intelligence.', price: 0, category: 'AI', isFree: true, attachmentUrl: 'https://example.com/ai-concepts.pdf', previewImageUrl: 'https://images.unsplash.com/photo-1526628953301-3e589a6a8b74?auto=format&fit=crop&w=500&q=60' },
                    { id: 'c7', mentorId: 'mentor7', title: 'UX Design Fundamentals', description: 'Learn the basics of User Experience design.', price: 79, category: 'UX/UI Design', isFree: false, attachmentUrl: 'https://example.com/ux-fundamentals.pdf', previewImageUrl: 'https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?auto=format&fit=crop&w=500&q=60' },
                ],
                oneOnOneSessions: [
                    { id: 's1', mentorId: 'mentor1', title: 'Physics Research Consultation', description: 'Get expert advice on your physics research project.', price: 150, previewImageUrl: 'https://images.unsplash.com/photo-1507146153580-69a1fe6d8aa1?auto=format&fit=crop&w=500&q=60' },
                    { id: 's2', mentorId: 'mentor2', title: 'Security Audit Guidance', description: 'Personalized guidance for your application security audit.', price: 200, previewImageUrl: 'https://images.unsplash.com/photo-1517694712202-14dd9538aa97?auto=format&fit=crop&w=500&q=60' },
                ],
                enrollments: [
                    { id: 'e1', geniusId: 'genius1', courseId: 'c1', status: 'enrolled' },
                    { id: 'e2', geniusId: 'genius1', courseId: 'c2', status: 'completed' },
                    { id: 'e3', geniusId: 'genius2', courseId: 'c2', status: 'in-progress' },
                    { id: 'e4', geniusId: 'genius2', courseId: 'c3', status: 'enrolled' },
                    { id: 'e5', geniusId: 'genius1', courseId: 'c6', status: 'completed' },
                    { id: 'e6', geniusId: 'genius1', courseId: 'c7', status: 'completed' },
                ]
            };
        }

        function getMentors() {
            const mentors = localStorage.getItem(MENTORS_KEY);
            return mentors ? JSON.parse(mentors) : [];
        }

        function getGeniuses() {
            const geniuses = localStorage.getItem(GENIUSES_KEY);
            return geniuses ? JSON.parse(geniuses) : [];
        }

        function getCourses() {
            const courses = localStorage.getItem(COURSES_KEY);
            return courses ? JSON.parse(courses) : [];
        }

        function getOneOnOneSessions() {
            const sessions = localStorage.getItem(ONE_ON_ONE_KEY);
            return sessions ? JSON.parse(sessions) : [];
        }

        function getEnrollments() {
            const enrollments = localStorage.getItem(ENROLLMENTS_KEY);
            return enrollments ? JSON.parse(enrollments) : [];
        }

        function getCourseCategories() {
            const courses = getCourses();
            const categories = new Set(courses.map(course => course.category));
            return Array.from(categories);
        }

        function addEnrollment(geniusId, courseId) {
            const enrollments = getEnrollments();
            const existingEnrollment = enrollments.find(e => e.geniusId === geniusId && e.courseId === courseId);
            
            if (existingEnrollment) {
                return existingEnrollment;
            }
            
            const newEnrollment = {
                id: `e${Date.now()}`,
                geniusId,
                courseId,
                status: 'enrolled'
            };
            
            enrollments.push(newEnrollment);
            localStorage.setItem(ENROLLMENTS_KEY, JSON.stringify(enrollments));
            return newEnrollment;
        }

        function updateEnrollmentStatus(enrollmentId, newStatus) {
            let enrollments = getEnrollments();
            const enrollmentIndex = enrollments.findIndex(e => e.id === enrollmentId);
            
            if (enrollmentIndex > -1) {
                enrollments[enrollmentIndex].status = newStatus;
                localStorage.setItem(ENROLLMENTS_KEY, JSON.stringify(enrollments));
                return enrollments[enrollmentIndex];
            }
            
            return null;
        }

        function getCompletedCoursesForGenius(geniusId) {
            const enrollments = getEnrollments();
            return enrollments.filter(e => e.geniusId === geniusId && e.status === 'completed');
        }

        // Page Renderers
        function renderHomePage() {
            const allMentors = getMentors();
            const gridItems = allMentors.slice(0, 8);
            
            return `
                <div class="main-container">
                    <header class="hero py-16 mb-12 text-center">
                        <h1 class="text-5xl md:text-7xl font-bold mb-6 text-brand-primary">Welcome to GigGenius University</h1>
                        <p class="text-xl md:text-2xl text-brand-secondary mb-6 max-w-3xl mx-auto">
                            Unlock your potential with expert-led courses from our vetted Mentors and personalized coaching.
                        </p>
                        <p class="text-md text-muted mb-10 max-w-3xl mx-auto">
                            GigGenius takes a 5% commission on courses sold. 30% of GigGenius's share goes directly to the GigGenius Charity, supporting underprivileged children.
                        </p>
                        <div class="space-x-4">
                            <a href="#" class="btn btn-primary btn-lg" onclick="navigateTo('genius-dashboard'); return false;">
                                Explore Courses (Geniuses)
                            </a>
                            <a href="#" class="btn btn-outline btn-lg" onclick="navigateTo('mentor-dashboard'); return false;">
                                Become a Mentor
                            </a>
                        </div>
                    </header>

                    <section class="grid md:grid-cols-3 gap-8 mb-12 text-center">
                        <div class="p-6 rounded-lg shadow-lg card">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-lg text-brand-primary mx-auto mb-4">
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                            </svg>
                            <h2 class="text-2xl font-semibold mb-2 text-brand-secondary">Expert Mentors</h2>
                            <p class="text-muted">Learn from industry leaders and experienced professionals offering courses.</p>
                        </div>
                        <div class="p-6 rounded-lg shadow-lg card">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-lg text-brand-primary mx-auto mb-4">
                                <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
                            </svg>
                            <h2 class="text-2xl font-semibold mb-2 text-brand-secondary">Ambitious Geniuses</h2>
                            <p class="text-muted">Our students, the Geniuses, are ready to learn and grow.</p>
                        </div>
                        <div class="p-6 rounded-lg shadow-lg card">
                            <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon-lg text-brand-primary mx-auto mb-4">
                                <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                                <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                            </svg>
                            <h2 class="text-2xl font-semibold mb-2 text-brand-secondary">Diverse Courses</h2>
                            <p class="text-muted">A wide range of free and paid courses to suit your needs.</p>
                        </div>
                    </section>
                    
                    <section class="my-16">
                        <h2 class="text-3xl md:text-4xl font-bold mb-8 text-brand-secondary text-center">Meet Our Mentors</h2>
                        <div class="grid grid-cols-2 md:grid-cols-4 gap-6">
                            ${gridItems.map((mentor, index) => `
                                <div class="p-4 rounded-lg shadow-lg card text-center flex flex-col items-center">
                                    <div class="avatar avatar-lg mb-3 border-2 border-brand-primary">
                                        <img src="https://avatar.vercel.sh/${mentor.avatarSeed}.png?size=96" alt="${mentor.name}">
                                    </div>
                                    <h3 class="text-lg font-semibold text-brand-secondary">${mentor.name}</h3>
                                    <p class="text-xs text-muted">${mentor.specialty}</p>
                                </div>
                            `).join('')}
                        </div>
                        <div class="text-center mt-12">
                            <button class="btn btn-primary btn-lg">
                                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                                    <circle cx="11" cy="11" r="8"></circle>
                                    <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                                </svg>
                                Explore More Mentors
                            </button>
                        </div>
                    </section>

                    <section class="text-center"> 
                        <img src="https://images.unsplash.com/photo-1507131679781-70be42a343e7" 
                            alt="Geniuses learning in a modern environment with GigGenius branding" 
                            class="rounded-lg shadow-lg mx-auto w-full max-w-4xl h-auto object-cover"
                            style="aspect-ratio: 16/9">
                    </section>
                </div>
            `;
        }

        function renderGeniusDashboard() {
            const allCourses = getCourses();
            const allSessions = getOneOnOneSessions();
            const mentorsData = getMentors().reduce((acc, mentor) => {
                acc[mentor.id] = mentor;
                return acc;
            }, {});
            
            const freeCourses = allCourses.filter(course => course.isFree);
            const paidCourses = allCourses.filter(course => !course.isFree);
            
            return `
                <div class="main-container">
                    <header class="mb-10 text-center">
                        <h1 class="text-4xl md:text-5xl font-bold mb-3 text-brand-primary">Genius Dashboard</h1>
                        <p class="text-lg text-brand-secondary">
                            Explore courses from expert Mentors, enroll, and book 1:1 coaching sessions.
                        </p>
                    </header>

                    <div class="mb-8 flex items-center space-x-4 p-4 bg-card rounded-lg shadow">
                        <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="icon text-brand-primary">
                            <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
                        </svg>
                        <select id="category-filter" class="form-control w-full max-w-xs" onchange="filterCourses()">
                            <option value="all">All Categories</option>
                            ${getCourseCategories().map(category => `
                                <option value="${category}">${category}</option>
                            `).join('')}
                        </select>
                    </div>

                    <section class="mb-12">
                        <h2 class="text-3xl font-semibold mb-6 flex items-center text-brand-secondary">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-3 text-green-500">
                                <circle cx="12" cy="8" r="7"></circle>
                                <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>
                            </svg>
                            Free Courses
                        </h2>
                        <div class="course-grid" id="free-courses-grid">
                            ${freeCourses.map(course => renderCourseCard(course, mentorsData[course.mentorId])).join('')}
                        </div>
                    </section>

                    <section class="mb-12">
                        <h2 class="text-3xl font-semibold mb-6 flex items-center text-brand-secondary">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-3 text-brand-primary">
                                <circle cx="12" cy="12" r="10"></circle>
                                <line x1="12" y1="8" x2="12" y2="12"></line>
                                <line x1="12" y1="16" x2="12.01" y2="16"></line>
                            </svg>
                            Paid Courses
                        </h2>
                        <div class="course-grid" id="paid-courses-grid">
                            ${paidCourses.map(course => renderCourseCard(course, mentorsData[course.mentorId])).join('')}
                        </div>
                    </section>

                    <section>
                        <h2 class="text-3xl font-semibold mb-6 flex items-center text-brand-secondary">
                            <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-3 text-brand-primary">
                                <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                <circle cx="9" cy="7" r="4"></circle>
                                <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                            </svg>
                            1:1 Coaching with Mentors
                        </h2>
                        <div class="course-grid">
                            ${allSessions.map(session => renderSessionCard(session, mentorsData[session.mentorId])).join('')}
                        </div>
                    </section>
                </div>
            `;
        }

        function renderMentorDashboard() {
            if (!currentUser || userRole !== 'mentor') {
                return '<div class="main-container"><p>Access denied. Please log in as a mentor.</p></div>';
            }
            
            const mentorId = currentUser.id;
            const courses = getCourses().filter(course => course.mentorId === mentorId);
            const sessions = getOneOnOneSessions().filter(session => session.mentorId === mentorId);
            const totalOfferings = courses.length + sessions.length;
            const canAddMore = totalOfferings < 5;
            
            return `
                <div class="main-container">
                    <header class="mb-10 text-center">
                        <h1 class="text-4xl md:text-5xl font-bold mb-3 text-brand-primary">Mentor Dashboard</h1>
                        <p class="text-lg text-brand-secondary">
                            Manage your courses and 1:1 sessions. Welcome, ${currentUser.name}!
                        </p>
                    </header>

                    <div class="card mb-8 p-6 bg-gradient-to-r from-primary/5 via-accent/5 to-secondary/5 shadow-lg">
                        <div class="flex flex-col md:flex-row justify-between items-center">
                            <div>
                                <h2 class="text-2xl font-semibold text-brand-primary flex items-center">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="16" x2="12" y2="12"></line>
                                        <line x1="12" y1="8" x2="12.01" y2="8"></line>
                                    </svg>
                                    Important Information
                                </h2>
                                <p class="text-brand-secondary mt-1">You have created ${totalOfferings} out of 5 allowed offerings.</p>
                                <p class="text-sm text-muted mt-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline mr-1 text-green-500">
                                        <circle cx="12" cy="12" r="10"></circle>
                                        <line x1="12" y1="8" x2="12" y2="12"></line>
                                        <line x1="12" y1="16" x2="12.01" y2="16"></line>
                                    </svg>
                                    Commissions: We take 5% on courses sold.
                                </p>
                                <p class="text-sm text-muted">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="inline mr-1 text-brand-primary">
                                        <rect x="2" y="7" width="20" height="14" rx="2" ry="2"></rect>
                                        <path d="M16 21V5a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16"></path>
                                    </svg>
                                    Charity: 30% of GigGenius's share goes to GigGenius Charity for underprivileged children.
                                </p>
                            </div>
                            <div class="mt-4 md:mt-0 space-x-2">
                                <button onclick="openCourseForm()" ${!canAddMore ? 'disabled' : ''} class="btn btn-primary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                                    </svg>
                                    Add New Course
                                </button>
                                <button onclick="openSessionForm()" ${!canAddMore ? 'disabled' : ''} class="btn btn-secondary">
                                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                                        <circle cx="9" cy="7" r="4"></circle>
                                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                                    </svg>
                                    Add 1:1 Session
                                </button>
                            </div>
                        </div>
                    </div>

                    <div class="tabs" id="mentor-tabs">
                        <div class="tab active" data-tab="courses" onclick="switchMentorTab('courses')">My Courses (${courses.length})</div>
                        <div class="tab" data-tab="sessions" onclick="switchMentorTab('sessions')">My 1:1 Sessions (${sessions.length})</div>
                        <div class="tab" data-tab="geniuses" onclick="switchMentorTab('geniuses')">My Geniuses (0)</div>
                    </div>

                    <div class="tab-content active" id="courses-tab">
                        ${courses.length > 0 ? `
                            <div class="course-grid">
                                ${courses.map(course => renderMentorCourseCard(course)).join('')}
                            </div>
                        ` : `
                            <p class="text-muted text-center py-8">You haven't created any courses yet. Click 'Add New Course' to get started!</p>
                        `}
                    </div>
                    
                    <div class="tab-content" id="sessions-tab">
                        ${sessions.length > 0 ? `
                            <div class="course-grid">
                                ${sessions.map(session => renderMentorSessionCard(session)).join('')}
                            </div>
                        ` : `
                            <p class="text-muted text-center py-8">You haven't created any 1:1 sessions yet. Click 'Add 1:1 Session' to offer personalized coaching!</p>
                        `}
                    </div>
                    
                    <div class="tab-content" id="geniuses-tab">
                        <p class="text-muted text-center py-8">No Geniuses have enrolled in your courses yet.</p>
                    </div>
                </div>
            `;
        }

        function renderNotFoundPage() {
            return `
                <div class="main-container text-center py-20">
                    <h1 class="text-6xl font-bold text-destructive mb-4">404</h1>
                    <p class="text-2xl text-brand-secondary mb-8">Oops! Page Not Found.</p>
                    <img src="https://images.unsplash.com/photo-1591439346001-847709bf07a7" 
                        alt="Confused person looking at a map with GigGenius logo" 
                        class="w-64 h-64 mx-auto mb-8">
                    <button onclick="navigateTo('home')" class="btn btn-primary btn-lg">Go to Homepage</button>
                </div>
            `;
        }

        // Component Renderers
        function renderCourseCard(course, mentor) {
            return `
                <div class="card course-card">
                    <div class="card-header">
                        <div class="flex items-center space-x-3 mb-2">
                            <div class="avatar">
                                <img src="https://avatar.vercel.sh/${mentor?.avatarSeed || 'unknown'}.png?size=40" alt="${mentor?.name || 'Unknown Mentor'}">
                            </div>
                            <div>
                                <h3 class="text-xl text-brand-secondary">${course.title}</h3>
                                <p class="text-sm text-muted">By ${mentor?.name || 'Unknown Mentor'}</p>
                            </div>
                        </div>
                        ${course.previewImageUrl ? `
                            <div class="aspect-video w-full rounded-md overflow-hidden mt-2">
                                <img src="${course.previewImageUrl}" alt="Preview for ${course.title}" class="w-full h-full object-cover">
                            </div>
                        ` : ''}
                    </div>
                    <div class="card-content">
                        <p class="text-muted text-sm mb-3">${course.description}</p>
                        <div class="flex justify-between items-center text-sm">
                            <span class="badge">${course.category}</span>
                            <span class="font-semibold ${course.isFree ? 'text-green-600' : 'text-brand-primary'}">
                                ${course.isFree ? 'Free' : `$${course.price}`}
                            </span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button onclick="previewCourse('${course.id}')" class="btn btn-primary w-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                                <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path>
                                <circle cx="12" cy="12" r="3"></circle>
                            </svg>
                            View Course
                        </button>
                    </div>
                </div>
            `;
        }

        function renderSessionCard(session, mentor) {
            return `
                <div class="card course-card">
                    <div class="card-header">
                        <div class="flex items-center space-x-3 mb-2">
                            <div class="avatar">
                                <img src="https://avatar.vercel.sh/${mentor?.avatarSeed || 'unknown'}.png?size=40" alt="${mentor?.name || 'Unknown Mentor'}">
                            </div>
                            <div>
                                <h3 class="text-xl text-brand-secondary">${session.title}</h3>
                                <p class="text-sm text-muted">With ${mentor?.name || 'Unknown Mentor'}</p>
                            </div>
                        </div>
                        ${session.previewImageUrl ? `
                            <div class="aspect-video w-full rounded-md overflow-hidden mt-2">
                                <img src="${session.previewImageUrl}" alt="Preview for ${session.title}" class="w-full h-full object-cover">
                            </div>
                        ` : ''}
                    </div>
                    <div class="card-content">
                        <p class="text-muted text-sm mb-3">${session.description}</p>
                        <div class="flex justify-between items-center text-sm">
                            <span class="badge">1:1 Coaching</span>
                            <span class="font-semibold text-brand-primary">$${session.price}</span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button onclick="bookSession('${session.id}')" class="btn btn-outline w-full">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2">
                                <path d="M15 10l5 5-5 5"></path>
                                <path d="M4 4v7a4 4 0 0 0 4 4h12"></path>
                            </svg>
                            Book Session
                        </button>
                    </div>
                </div>
            `;
        }

        function renderMentorCourseCard(course) {
            return `
                <div class="card course-card">
                    <div class="card-header bg-primary/10">
                        <h3 class="text-lg text-brand-secondary">${course.title}</h3>
                        <p class="text-xs">Course (${course.isFree ? 'Free' : 'Paid'})</p>
                        ${course.previewImageUrl ? `
                            <div class="aspect-video w-full rounded-md overflow-hidden mt-2">
                                <img src="${course.previewImageUrl}" alt="Preview for ${course.title}" class="w-full h-full object-cover">
                            </div>
                        ` : ''}
                    </div>
                    <div class="card-content">
                        <p class="text-muted text-sm mb-2">${course.description}</p>
                        <div class="flex justify-between items-center text-sm">
                            <span class="font-semibold text-brand-primary">$${course.price}</span>
                            ${course.category ? `<span class="badge">${course.category}</span>` : ''}
                        </div>
                        ${course.attachmentUrl ? `<p class="text-xs text-muted mt-2 truncate">Attachment: ${course.attachmentUrl}</p>` : ''}
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-outline" onclick="editCourse('${course.id}')">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                            Edit
                        </button>
                        <button class="btn btn-outline text-destructive" onclick="deleteCourse('${course.id}')">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                <path d="M3 6h18"></path>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                            </svg>
                            Delete
                        </button>
                    </div>
                </div>
            `;
        }

        function renderMentorSessionCard(session) {
            return `
                <div class="card course-card">
                    <div class="card-header bg-secondary/10">
                        <h3 class="text-lg text-brand-secondary">${session.title}</h3>
                        <p class="text-xs">1:1 Session</p>
                        ${session.previewImageUrl ? `
                            <div class="aspect-video w-full rounded-md overflow-hidden mt-2">
                                <img src="${session.previewImageUrl}" alt="Preview for ${session.title}" class="w-full h-full object-cover">
                            </div>
                        ` : ''}
                    </div>
                    <div class="card-content">
                        <p class="text-muted text-sm mb-2">${session.description}</p>
                        <div class="flex justify-between items-center text-sm">
                            <span class="font-semibold text-brand-primary">$${session.price}</span>
                            <span class="badge">1:1 Coaching</span>
                        </div>
                    </div>
                    <div class="card-footer">
                        <button class="btn btn-outline" onclick="editSession('${session.id}')">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"></path>
                                <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"></path>
                            </svg>
                            Edit
                        </button>
                        <button class="btn btn-outline text-destructive" onclick="deleteSession('${session.id}')">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1">
                                <path d="M3 6h18"></path>
                                <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                                <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                            </svg>
                            Delete
                        </button>
                    </div>
                </div>
            `;
        }

        // Action Handlers
        function previewCourse(courseId) {
            if (!isAuthenticated) {
                showToast('Login Required', 'Please log in to view course details.', 'error');
                openAuthModal('login');
                return;
            }
            
            const course = getCourses().find(c => c.id === courseId);
            if (!course) {
                showToast('Error', 'Course not found.', 'error');
                return;
            }
            
            const mentor = getMentors().find(m => m.id === course.mentorId);
            
            showToast('Course Preview', `You are viewing "${course.title}" by ${mentor?.name || 'Unknown Mentor'}.`, 'success');
            
            // In a real app, this would open a modal with course details
            if (course.isFree) {
                const enrollment = addEnrollment(currentUser.id, courseId);
                showToast('Enrolled Successfully!', `You have enrolled in "${course.title}".`, 'success');
                
                // Mock auto-completion for free courses
                updateEnrollmentStatus(enrollment.id, 'completed');
            } else {
                showToast('Payment Required', `This course costs $${course.price}.`, 'success');
            }
        }

        function bookSession(sessionId) {
            if (!isAuthenticated) {
                showToast('Login Required', 'Please log in to book sessions.', 'error');
                openAuthModal('login');
                return;
            }
            
            const session = getOneOnOneSessions().find(s => s.id === sessionId);
            if (!session) {
                showToast('Error', 'Session not found.', 'error');
                return;
            }
            
            const mentor = getMentors().find(m => m.id === session.mentorId);
            
            showToast('Session Booking (Mock)', `You've initiated booking for "${session.title}" with ${mentor?.name || 'Unknown Mentor'}.`, 'success');
        }

        function switchMentorTab(tab) {
            // Update tabs
            document.querySelectorAll('#mentor-tabs .tab').forEach(t => {
                t.classList.remove('active');
            });
            document.querySelector(`#mentor-tabs .tab[data-tab="${tab}"]`).classList.add('active');
            
            // Update content
            document.querySelectorAll('.tab-content').forEach(c => {
                c.classList.remove('active');
            });
            document.getElementById(`${tab}-tab`).classList.add('active');
        }

        function filterCourses() {
            // This would filter courses based on selected category
            showToast('Filter Applied', 'Course filtering is mocked in this demo.', 'success');
        }

        function openCourseForm() {
            showToast('Form (Mock)', 'Course creation form would open here.', 'success');
        }

        function openSessionForm() {
            showToast('Form (Mock)', 'Session creation form would open here.', 'success');
        }

        function editCourse(courseId) {
            showToast('Edit (Mock)', `Editing course ${courseId}.`, 'success');
        }

        function deleteCourse(courseId) {
            showToast('Delete (Mock)', `Deleting course ${courseId}.`, 'success');
        }

        function editSession(sessionId) {
            showToast('Edit (Mock)', `Editing session ${sessionId}.`, 'success');
        }

        function deleteSession(sessionId) {
            showToast('Delete (Mock)', `Deleting session ${sessionId}.`, 'success');
        }
    </script>
</body>
</html>